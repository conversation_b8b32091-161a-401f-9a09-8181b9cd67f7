// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`CarouselCard Component Basic rendering should render with multiple children and matches snapshot 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 min-h-0 grid-rows-1"
    data-slot="carousel"
    role="region"
  >
    <div
      class="relative max-sm:-mx-[var(--page-padding)] sm:rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <div
        class="overflow-hidden"
        data-slot="carousel-content"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            data-slot="carousel-item"
            role="group"
          >
            <article
              class="rounded *:p-8 h-full [&>div]:h-full"
            >
              <div>
                Child 1
              </div>
              <div>
                Child 2
              </div>
              <div>
                Child 3
              </div>
            </article>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component Basic rendering should render with single child and matches snapshot 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 min-h-0 grid-rows-1"
    data-slot="carousel"
    role="region"
  >
    <div
      class="relative max-sm:-mx-[var(--page-padding)] sm:rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <div
        class="overflow-hidden"
        data-slot="carousel-content"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            data-slot="carousel-item"
            role="group"
          >
            <article
              class="rounded *:p-8 h-full [&>div]:h-full"
            >
              <div>
                Single Child Content
              </div>
            </article>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component Navigation visibility should not show navigation controls with single child 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 min-h-0 grid-rows-1"
    data-slot="carousel"
    role="region"
  >
    <div
      class="relative max-sm:-mx-[var(--page-padding)] sm:rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <div
        class="overflow-hidden"
        data-slot="carousel-content"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            data-slot="carousel-item"
            role="group"
          >
            <article
              class="rounded *:p-8 h-full [&>div]:h-full"
            >
              <div>
                Single item - no nav needed
              </div>
            </article>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component Navigation visibility should show navigation controls with multiple children 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 min-h-0 grid-rows-1"
    data-slot="carousel"
    role="region"
  >
    <div
      class="relative max-sm:-mx-[var(--page-padding)] sm:rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <div
        class="overflow-hidden"
        data-slot="carousel-content"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            data-slot="carousel-item"
            role="group"
          >
            <article
              class="rounded *:p-8 h-full [&>div]:h-full"
            >
              <div>
                Item 1
              </div>
              <div>
                Item 2
              </div>
            </article>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component With custom className should render with custom className and title 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid min-h-0 grid-rows-[auto_1fr] w-full h-96"
    data-slot="carousel"
    role="region"
  >
    <h2
      class="text-xl py-6 text-gray-500 font-normal"
    >
      Custom Styled Carousel
    </h2>
    <div
      class="relative max-sm:-mx-[var(--page-padding)] sm:rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <div
        class="overflow-hidden"
        data-slot="carousel-content"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            data-slot="carousel-item"
            role="group"
          >
            <article
              class="rounded *:p-8 h-full [&>div]:h-full"
            >
              <div>
                Item 1
              </div>
              <div>
                Item 2
              </div>
            </article>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component With overlay should render with overlay only 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 min-h-0 grid-rows-1"
    data-slot="carousel"
    role="region"
  >
    <div
      class="relative max-sm:-mx-[var(--page-padding)] sm:rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <div
        class="overflow-hidden"
        data-slot="carousel-content"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            data-slot="carousel-item"
            role="group"
          >
            <article
              class="rounded *:p-8 h-full [&>div]:h-full"
            >
              <div>
                Content with overlay
              </div>
              <div>
                Another item
              </div>
            </article>
          </div>
        </div>
      </div>
      <div
        class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center"
      >
        <span
          class="text-white"
        >
          Overlay Content
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component With overlay should render with title and overlay 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 min-h-0 grid-rows-[auto_1fr]"
    data-slot="carousel"
    role="region"
  >
    <h2
      class="text-xl py-6 text-gray-500 font-normal"
    >
      Carousel with Overlay
    </h2>
    <div
      class="relative max-sm:-mx-[var(--page-padding)] sm:rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <div
        class="overflow-hidden"
        data-slot="carousel-content"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            data-slot="carousel-item"
            role="group"
          >
            <article
              class="rounded *:p-8 h-full [&>div]:h-full"
            >
              <div>
                Item 1
              </div>
              <div>
                Item 2
              </div>
            </article>
          </div>
        </div>
      </div>
      <div
        class="absolute top-0 right-0 p-2"
      >
        <button>
          Close
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component With title should render with title and multiple children 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 min-h-0 grid-rows-[auto_1fr]"
    data-slot="carousel"
    role="region"
  >
    <h2
      class="text-xl py-6 text-gray-500 font-normal"
    >
      Multiple Items Carousel
    </h2>
    <div
      class="relative max-sm:-mx-[var(--page-padding)] sm:rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <div
        class="overflow-hidden"
        data-slot="carousel-content"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            data-slot="carousel-item"
            role="group"
          >
            <article
              class="rounded *:p-8 h-full [&>div]:h-full"
            >
              <div>
                Item 1
              </div>
              <div>
                Item 2
              </div>
              <div>
                Item 3
              </div>
            </article>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component With title should render with title and single child 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 min-h-0 grid-rows-[auto_1fr]"
    data-slot="carousel"
    role="region"
  >
    <h2
      class="text-xl py-6 text-gray-500 font-normal"
    >
      Test Carousel Title
    </h2>
    <div
      class="relative max-sm:-mx-[var(--page-padding)] sm:rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <div
        class="overflow-hidden"
        data-slot="carousel-content"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            data-slot="carousel-item"
            role="group"
          >
            <article
              class="rounded *:p-8 h-full [&>div]:h-full"
            >
              <div>
                Content with title
              </div>
            </article>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component Without title should render without title 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 min-h-0 grid-rows-1"
    data-slot="carousel"
    role="region"
  >
    <div
      class="relative max-sm:-mx-[var(--page-padding)] sm:rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <div
        class="overflow-hidden"
        data-slot="carousel-content"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            data-slot="carousel-item"
            role="group"
          >
            <article
              class="rounded *:p-8 h-full [&>div]:h-full"
            >
              <div>
                Content without title
              </div>
              <div>
                Another item
              </div>
            </article>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
